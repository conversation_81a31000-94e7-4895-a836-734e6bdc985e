class TestClass {
    private int value;
    
    // 构造函数
    TestClass(int val) {
        this.value = val;
        this.initialize();
    }
    
    // 私有函数
    private void initialize() {
        this.value = 0;
        this.processData();
    }
    
    // 公共函数
    public void processData() {
        if (this.value > 0) {
            this.helper();
        }
    }
    
    // 辅助函数
    private void helper() {
        this.value++;
        this.processData(); // 递归调用
    }
    
    // 获取值
    public int getValue() {
        return this.value;
    }
    
    // 设置值
    public void setValue(int newValue) {
        this.value = newValue;
        this.processData();
    }
}

class AnotherClass {
    private TestClass testObj;
    
    AnotherClass() {
        this.testObj = TestClass(10);
    }
    
    void someMethod() {
        this.testObj.setValue(20);
        int val = this.testObj.getValue();
    }
    
    // 这个函数名和 TestClass 中的函数同名，但应该不会被包含在 TestClass 的引用中
    private void helper() {
        // 这里不应该被 TestClass.helper 的引用查找包含
    }
}
