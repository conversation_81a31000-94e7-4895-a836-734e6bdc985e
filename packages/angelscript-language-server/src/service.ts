import type {
    LanguageServicePlugin,
    LanguageServicePluginInstance,
    Location,
    LocationLink,
    Hover,
} from '@volar/language-service';
import { URI } from 'vscode-uri';
import * as fs from 'fs';
import * as path from 'path';

function findFileInWorkspace(
    workspaceFolders: string[],
    includePath: string,
): string | undefined {
    for (const folder of workspaceFolders) {
        const foundPath = searchFileRecursive(folder, includePath);
        if (foundPath) {
            return foundPath;
        }
    }
    return undefined;
}

function searchFileRecursive(
    dir: string,
    targetFile: string,
): string | undefined {
    const files = fs.readdirSync(dir);
    for (const file of files) {
        const fullPath = path.join(dir, file);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
            const result = searchFileRecursive(fullPath, targetFile);
            if (result) {
                return result;
            }
        } else if (file === targetFile) {
            return fullPath;
        }
    }
    return undefined;
}

function findFunctionDefinition(
    lines: string[],
    functionName: string,
): { line: number; character: number } | undefined {
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 跳过注释行和空行
        if (line.startsWith('//') || line.startsWith('/*') || line === '') {
            continue;
        }

        // 匹配各种函数定义模式，确保是定义而不是调用
        const patterns = [
            // 带访问修饰符的函数: private/public/protected 返回类型 函数名(参数) {
            new RegExp(`^(private|public|protected)\\s+\\w+\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // 普通函数: 返回类型 函数名(参数) {
            new RegExp(`^\\w+\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // void 函数: void 函数名(参数) {
            new RegExp(`^void\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // 构造函数: 类名(参数) {
            new RegExp(`^${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
        ];

        for (const pattern of patterns) {
            if (pattern.test(line)) {
                // 确保不是在字符串或注释中
                const originalLine = lines[i];
                const functionNameIndex = originalLine.indexOf(functionName);

                // 检查是否在注释中
                const commentIndex = originalLine.indexOf('//');
                if (commentIndex !== -1 && functionNameIndex > commentIndex) {
                    continue;
                }

                return {
                    line: i,
                    character: functionNameIndex,
                };
            }
        }
    }
    return undefined;
}

function findCurrentClassRange(
    lines: string[],
    currentLine: number,
): { start: number; end: number; className: string } | undefined {
    let classStart = -1;
    let className = '';
    let braceCount = 0;
    let inClass = false;

    // 向上查找 class 定义
    for (let i = currentLine; i >= 0; i--) {
        const line = lines[i].trim();

        // 跳过注释行和空行
        if (line.startsWith('//') || line.startsWith('/*') || line === '') {
            continue;
        }

        // 匹配 class 定义
        const classMatch = line.match(/^class\s+(\w+)(?:\s*:\s*\w+)?\s*\{?/);
        if (classMatch) {
            classStart = i;
            className = classMatch[1];
            inClass = true;
            break;
        }
    }

    if (!inClass || classStart === -1) {
        return undefined;
    }

    // 从 class 开始向下查找对应的结束大括号
    braceCount = 0;
    let foundFirstBrace = false;

    for (let i = classStart; i < lines.length; i++) {
        const line = lines[i];

        for (let j = 0; j < line.length; j++) {
            const char = line[j];
            if (char === '{') {
                braceCount++;
                foundFirstBrace = true;
            } else if (char === '}') {
                braceCount--;
                if (foundFirstBrace && braceCount === 0) {
                    return {
                        start: classStart,
                        end: i,
                        className: className,
                    };
                }
            }
        }
    }

    // 如果没有找到结束大括号，返回到文件末尾
    return {
        start: classStart,
        end: lines.length - 1,
        className: className,
    };
}

function isFunctionCall(line: string, word: string, position: number): boolean {
    // 检查单词后面是否紧跟着开括号（可能有空格）
    const afterWord = line.substring(position + word.length);
    const parenMatch = afterWord.match(/^\s*\(/);
    return parenMatch !== null;
}

function isFunctionDefinition(line: string, word: string): boolean {
    const trimmedLine = line.trim();

    // 匹配各种函数定义模式
    const patterns = [
        // 带访问修饰符的函数: private/public/protected 返回类型 函数名(参数) {
        new RegExp(`^(private|public|protected)\\s+\\w+\\s+${word}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
        // 普通函数: 返回类型 函数名(参数) {
        new RegExp(`^\\w+\\s+${word}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
        // void 函数: void 函数名(参数) {
        new RegExp(`^void\\s+${word}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
        // 构造函数: 类名(参数) {
        new RegExp(`^${word}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
    ];

    return patterns.some(pattern => pattern.test(trimmedLine));
}

export const service: LanguageServicePlugin = {
    name: 'angelscript-service',
    capabilities: {
        hoverProvider: true,
        definitionProvider: true,
        referencesProvider: true,
    },
    create(context): LanguageServicePluginInstance {
        console.log('angelscript-service created!');
        const workspaceFolders = (context.env?.workspaceFolders || []).map(
            (folder) =>
                typeof folder === 'string'
                    ? URI.parse(folder).fsPath
                    : folder.fsPath,
        );
        return {
            provideDefinition(document, position, token) {
                const lineText = document.getText().split('\n')[position.line];
                const char = position.character;

                // 查找光标下的单词
                let start = char;
                while (start > 0 && /\w/.test(lineText[start - 1])) {
                    start--;
                }
                let end = char;
                while (end < lineText.length && /\w/.test(lineText[end])) {
                    end++;
                }
                const word = lineText.substring(start, end);

                if (!word) {
                    return [];
                }

                const documentText = document.getText();
                const lines = documentText.split('\n');

                // 检查是否为函数调用
                const functionCallMatch = lineText.match(new RegExp(`\\b${word}\\s*\\(`));
                if (functionCallMatch) {
                    // 确保不是函数定义行
                    const trimmedLine = lineText.trim();
                    const isDefinition = /^(private|public|protected)?\s*\w*\s*\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine) ||
                                        /^void\s+\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine) ||
                                        /^\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine);
                    
                    if (!isDefinition) {
                        const functionDef = findFunctionDefinition(lines, word);
                        if (functionDef) {
                            const targetRange = {
                                start: { line: functionDef.line, character: functionDef.character },
                                end: { 
                                    line: functionDef.line, 
                                    character: functionDef.character + word.length 
                                },
                            };
                            return [{
                                targetUri: document.uri,
                                targetRange,
                                targetSelectionRange: targetRange,
                            }] as LocationLink[];
                        }
                    }
                }

                // 检查是否为类继承语法
                const classInheritanceMatch = lineText.match(/class\s+\w+\s*:\s*(\w+)/);
                if (classInheritanceMatch && classInheritanceMatch[1] === word) {
                    const baseClassName = word;
                    const documentText = document.getText();
                    const includeRegex = /#include\s+"(.+?)"/g;
                    let match;
                    const includes: string[] = [];
                    while ((match = includeRegex.exec(documentText)) !== null) {
                        includes.push(match[1]);
                    }

                    for (const includePath of includes) {
                        const matchedFilePath = findFileInWorkspace(
                            workspaceFolders,
                            includePath,
                        );
                        if (matchedFilePath) {
                            const fileContent = fs.readFileSync(
                                matchedFilePath,
                                'utf-8',
                            );
                            const lines = fileContent.split('\n');
                            for (let i = 0; i < lines.length; i++) {
                                const lineContent = lines[i];
                                const classDefMatch = lineContent.match(
                                    new RegExp(`^\\s*class\\s+${baseClassName}\\b`),
                                );
                                if (classDefMatch) {
                                    const targetUri = URI.file(
                                        matchedFilePath,
                                    ).toString();
                                    const targetRange = {
                                        start: { line: i, character: 0 },
                                        end: {
                                            line: i,
                                            character: lineContent.length,
                                        },
                                    };
                                    return [
                                        {
                                            targetUri,
                                            targetRange,
                                            targetSelectionRange: targetRange,
                                        },
                                    ] as LocationLink[];
                                }
                            }
                        }
                    }
                }

                // 匹配 #include "" 语法
                const includeMatch = lineText.match(/#include\s+"(.+?)"/);
                if (includeMatch) {
                    const includePath = includeMatch[1];

                    const matchedFilePath = findFileInWorkspace(
                        workspaceFolders,
                        includePath,
                    );

                    if (matchedFilePath) {
                        // 创建目标文件的URI
                        const targetUri = URI.file(matchedFilePath).toString();

                        // 获取完整文件名（包含后缀）
                        const fileName = path.basename(matchedFilePath);

                        // 创建包含完整文件名长度的Range
                        const targetRange = {
                            start: {
                                line: 0,
                                character: 0,
                            },
                            end: {
                                line: 0,
                                character: fileName.length,
                            },
                        };

                        return [
                            {
                                targetUri,
                                targetRange,
                                targetSelectionRange: targetRange,
                            },
                        ] as LocationLink[];
                    }

                    return [] as LocationLink[];
                }

                return [];
            },
            provideReferences(document, position, context) {
                try {
                    console.log('provideReferences called for position:', position);
                    console.log('Document URI:', document.uri);
                    const lineText = document.getText().split('\n')[position.line];
                    const char = position.character;
                    console.log('Line text:', lineText);

                // 查找光标下的单词
                let start = char;
                while (start > 0 && /\w/.test(lineText[start - 1])) {
                    start--;
                }
                let end = char;
                while (end < lineText.length && /\w/.test(lineText[end])) {
                    end++;
                }
                const word = lineText.substring(start, end);
                console.log('Word under cursor:', word);

                if (!word) {
                    console.log('No word found, returning empty array');
                    return [];
                }

                const documentText = document.getText();
                const lines = documentText.split('\n');

                // 查找当前光标所在的 class 范围
                const classRange = findCurrentClassRange(lines, position.line);
                console.log('Class range:', classRange);

                // 暂时注释掉 class 范围检查，允许在整个文件中查找
                // if (!classRange) {
                //     // 如果不在 class 内，返回空结果
                //     console.log('Not in class, returning empty array');
                //     return [];
                // }

                // 检查当前单词是否是函数（通过检查是否有函数定义或调用）
                const isFunctionContext = isFunctionDefinition(lineText, word) ||
                                        isFunctionCall(lineText, word, start);
                console.log('Is function context:', isFunctionContext);

                // 暂时注释掉这个检查，允许所有单词进行引用查找
                // if (!isFunctionContext) {
                //     // 如果不是函数上下文，返回空结果
                //     console.log('Not function context, returning empty array');
                //     return [];
                // }

                const locations: Location[] = [];

                // 确定搜索范围
                const searchStart = classRange ? classRange.start : 0;
                const searchEnd = classRange ? classRange.end : lines.length - 1;
                console.log('Search range:', searchStart, 'to', searchEnd);

                // 在指定范围内查找引用
                for (let i = searchStart; i <= searchEnd; i++) {
                    const line = lines[i];
                    let col = 0;

                    while ((col = line.indexOf(word, col)) !== -1) {
                        // 确保找到的是整个单词，而不是子字符串
                        const prevChar = col > 0 ? line[col - 1] : ' ';
                        const nextChar =
                            col + word.length < line.length
                                ? line[col + word.length]
                                : ' ';

                        if (!/\w/.test(prevChar) && !/\w/.test(nextChar)) {
                            // 暂时简化检查，添加所有匹配的单词
                            locations.push({
                                uri: document.uri,
                                range: {
                                    start: { line: i, character: col },
                                    end: {
                                        line: i,
                                        character: col + word.length,
                                    },
                                },
                            });
                        }
                        col += word.length;
                    }
                }

                return locations;
            },
            provideHover(document, position) {
                console.log('provideHover called for position:', position);
                const lineText = document.getText().split('\n')[position.line];
                const char = position.character;

                // 查找光标下的单词
                let start = char;
                while (start > 0 && /\w/.test(lineText[start - 1])) {
                    start--;
                }
                let end = char;
                while (end < lineText.length && /\w/.test(lineText[end])) {
                    end++;
                }
                const word = lineText.substring(start, end);
                console.log('Hover word:', word);

                if (!word) {
                    console.log('No word for hover, returning undefined');
                    return undefined;
                }

                // 简化 hover 功能，提供基本的单词信息
                const hoverContent = `**Symbol**: \`${word}\`\n\nLine: ${position.line + 1}, Character: ${position.character + 1}`;

                return {
                    contents: {
                        kind: 'markdown',
                        value: hoverContent,
                    },
                    range: {
                        start: { line: position.line, character: start },
                        end: { line: position.line, character: end },
                    },
                } as Hover;
            },
        };
    },
};
