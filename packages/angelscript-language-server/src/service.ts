import type {
    LanguageServicePlugin,
    LanguageServicePluginInstance,
    Location,
    LocationLink,
} from '@volar/language-service';
import { URI } from 'vscode-uri';
import * as fs from 'fs';
import * as path from 'path';

function findFileInWorkspace(
    workspaceFolders: string[],
    includePath: string,
): string | undefined {
    for (const folder of workspaceFolders) {
        const foundPath = searchFileRecursive(folder, includePath);
        if (foundPath) {
            return foundPath;
        }
    }
    return undefined;
}

function searchFileRecursive(
    dir: string,
    targetFile: string,
): string | undefined {
    const files = fs.readdirSync(dir);
    for (const file of files) {
        const fullPath = path.join(dir, file);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
            const result = searchFileRecursive(fullPath, targetFile);
            if (result) return result;
        } else if (file === targetFile) {
            return fullPath;
        }
    }
    return undefined;
}

function findFunctionDefinition(
    lines: string[],
    functionName: string,
): { line: number; character: number } | undefined {
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // 跳过注释行和空行
        if (line.startsWith('//') || line.startsWith('/*') || line === '') {
            continue;
        }
        
        // 匹配各种函数定义模式，确保是定义而不是调用
        const patterns = [
            // 带访问修饰符的函数: private/public/protected 返回类型 函数名(参数) {
            new RegExp(`^(private|public|protected)\\s+\\w+\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // 普通函数: 返回类型 函数名(参数) {
            new RegExp(`^\\w+\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // void 函数: void 函数名(参数) {
            new RegExp(`^void\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
            // 构造函数: 类名(参数) {
            new RegExp(`^${functionName}\\s*\\([^)]*\\)\\s*\\{?\\s*$`),
        ];

        for (const pattern of patterns) {
            if (pattern.test(line)) {
                // 确保不是在字符串或注释中
                const originalLine = lines[i];
                const functionNameIndex = originalLine.indexOf(functionName);
                
                // 检查是否在注释中
                const commentIndex = originalLine.indexOf('//');
                if (commentIndex !== -1 && functionNameIndex > commentIndex) {
                    continue;
                }
                
                return {
                    line: i,
                    character: functionNameIndex,
                };
            }
        }
    }
    return undefined;
}

export const service: LanguageServicePlugin = {
    name: 'angelscript-service',
    capabilities: {
        hoverProvider: true,
        definitionProvider: true,
        referencesProvider: true,
    },
    create(context): LanguageServicePluginInstance {
        console.log('angelscript-service created!');
        const workspaceFolders = (context.env?.workspaceFolders || []).map(
            (folder) =>
                typeof folder === 'string'
                    ? URI.parse(folder).fsPath
                    : folder.fsPath,
        );
        return {
            provideDefinition(document, position, token) {
                const lineText = document.getText().split('\n')[position.line];
                const char = position.character;

                // 查找光标下的单词
                let start = char;
                while (start > 0 && /\w/.test(lineText[start - 1])) {
                    start--;
                }
                let end = char;
                while (end < lineText.length && /\w/.test(lineText[end])) {
                    end++;
                }
                const word = lineText.substring(start, end);

                if (!word) return [];

                const documentText = document.getText();
                const lines = documentText.split('\n');

                // 检查是否为函数调用
                const functionCallMatch = lineText.match(new RegExp(`\\b${word}\\s*\\(`));
                if (functionCallMatch) {
                    // 确保不是函数定义行
                    const trimmedLine = lineText.trim();
                    const isDefinition = /^(private|public|protected)?\s*\w*\s*\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine) ||
                                        /^void\s+\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine) ||
                                        /^\w+\s*\([^)]*\)\s*\{?\s*$/.test(trimmedLine);
                    
                    if (!isDefinition) {
                        const functionDef = findFunctionDefinition(lines, word);
                        if (functionDef) {
                            const targetRange = {
                                start: { line: functionDef.line, character: functionDef.character },
                                end: { 
                                    line: functionDef.line, 
                                    character: functionDef.character + word.length 
                                },
                            };
                            return [{
                                targetUri: document.uri,
                                targetRange,
                                targetSelectionRange: targetRange,
                            }] as LocationLink[];
                        }
                    }
                }

                // 检查是否为类继承语法
                const classInheritanceMatch = lineText.match(/class\s+\w+\s*:\s*(\w+)/);
                if (classInheritanceMatch && classInheritanceMatch[1] === word) {
                    const baseClassName = word;
                    const documentText = document.getText();
                    const includeRegex = /#include\s+"(.+?)"/g;
                    let match;
                    const includes: string[] = [];
                    while ((match = includeRegex.exec(documentText)) !== null) {
                        includes.push(match[1]);
                    }

                    for (const includePath of includes) {
                        const matchedFilePath = findFileInWorkspace(
                            workspaceFolders,
                            includePath,
                        );
                        if (matchedFilePath) {
                            const fileContent = fs.readFileSync(
                                matchedFilePath,
                                'utf-8',
                            );
                            const lines = fileContent.split('\n');
                            for (let i = 0; i < lines.length; i++) {
                                const lineContent = lines[i];
                                const classDefMatch = lineContent.match(
                                    new RegExp(`^\\s*class\\s+${baseClassName}\\b`),
                                );
                                if (classDefMatch) {
                                    const targetUri = URI.file(
                                        matchedFilePath,
                                    ).toString();
                                    const targetRange = {
                                        start: { line: i, character: 0 },
                                        end: {
                                            line: i,
                                            character: lineContent.length,
                                        },
                                    };
                                    return [
                                        {
                                            targetUri,
                                            targetRange,
                                            targetSelectionRange: targetRange,
                                        },
                                    ] as LocationLink[];
                                }
                            }
                        }
                    }
                }

                // 匹配 #include "" 语法
                const includeMatch = lineText.match(/#include\s+"(.+?)"/);
                if (includeMatch) {
                    const includePath = includeMatch[1];

                    const matchedFilePath = findFileInWorkspace(
                        workspaceFolders,
                        includePath,
                    );

                    if (matchedFilePath) {
                        // 创建目标文件的URI
                        const targetUri = URI.file(matchedFilePath).toString();

                        // 获取完整文件名（包含后缀）
                        const fileName = path.basename(matchedFilePath);

                        // 创建包含完整文件名长度的Range
                        const targetRange = {
                            start: {
                                line: 0,
                                character: 0,
                            },
                            end: {
                                line: 0,
                                character: fileName.length,
                            },
                        };

                        return [
                            {
                                targetUri,
                                targetRange,
                                targetSelectionRange: targetRange,
                            },
                        ] as LocationLink[];
                    }

                    return [] as LocationLink[];
                }

                return [];
            },
            provideReferences(document, position, context) {
                const lineText = document.getText().split('\n')[position.line];
                const char = position.character;

                // 查找光标下的单词
                let start = char;
                while (start > 0 && /\w/.test(lineText[start - 1])) {
                    start--;
                }
                let end = char;
                while (end < lineText.length && /\w/.test(lineText[end])) {
                    end++;
                }
                const word = lineText.substring(start, end);

                if (!word) {
                    return [];
                }

                const documentText = document.getText();
                const includeRegex = /#include\s+"(.+?)"/g;
                let match;
                const filesToSearch: string[] = [
                    URI.parse(document.uri).fsPath,
                ]; // Include current file

                while ((match = includeRegex.exec(documentText)) !== null) {
                    const includePath = match[1];
                    const matchedFilePath = findFileInWorkspace(
                        workspaceFolders,
                        includePath,
                    );
                    if (matchedFilePath) {
                        filesToSearch.push(matchedFilePath);
                    }
                }

                const locations: Location[] = [];

                for (const filePath of [...new Set(filesToSearch)]) {
                    // Use Set to remove duplicates
                    const fileContent = fs.readFileSync(filePath, 'utf-8');
                    const lines = fileContent.split('\n');
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];
                        let col = 0;
                        while ((col = line.indexOf(word, col)) !== -1) {
                            // 确保找到的是整个单词，而不是子字符串
                            const prevChar = col > 0 ? line[col - 1] : ' ';
                            const nextChar =
                                col + word.length < line.length
                                    ? line[col + word.length]
                                    : ' ';
                            if (!/\w/.test(prevChar) && !/\w/.test(nextChar)) {
                                locations.push({
                                    uri: URI.file(filePath).toString(),
                                    range: {
                                        start: { line: i, character: col },
                                        end: {
                                            line: i,
                                            character: col + word.length,
                                        },
                                    },
                                });
                            }
                            col += word.length;
                        }
                    }
                }

                return locations;
            },
        };
    },
};
